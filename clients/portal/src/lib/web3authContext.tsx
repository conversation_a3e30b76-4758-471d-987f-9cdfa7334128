import { type Web3AuthContextConfig } from "@web3auth/modal/react";
import { WALLET_CONNECTORS, WEB3AUTH_NETWORK } from "@web3auth/modal";

export const createWeb3AuthConfig = (): Web3AuthContextConfig => {
  const id = import.meta.env.VITE_WEB3AUTH_CLIENT_ID;
  
  console.log("ID ", id);
  
  return {
    web3AuthOptions: {
      clientId: id,
      web3AuthNetwork: WEB3AUTH_NETWORK.SAPPHIRE_DEVNET,
      modalConfig: {
        connectors: {
          [WALLET_CONNECTORS.AUTH]: {
            label: "auth",
            loginMethods: {
              email_passwordless: {
                name: "email passwordless login",
                authConnectionId: "w3a-email-passwordless-demo"
              },
              sms_passwordless: {
                name: "sms passwordless login",
                authConnectionId: "w3a-sms-passwordless-dev-demo"
              }
            },
          }
        },
      },
    },
  };
};