import { Button } from "@/components/ui/button";
// import { DotLottieReact } from "@lottiefiles/dotlottie-react";
export const AuthPage = () => {
  return (
    <>
      <div className="fixed inset-0 -z-10 w-screen h-screen overflow-hidden">
        {/* <DotLottieReact
          src="https://lottie.host/8a4d2c22-e8f8-4c23-9b21-925bab4730bb/fQmn1KLMmP.lottie"
          autoplay
          loop
          speed={1}
        //   style={{ width: "100%", height: "100%", objectFit: "cover" }}
        /> */}
      </div>
      <div className="w-screen h-screen flex justify-center items-center">
        <Button>
            Login
        </Button>
      </div>
    </>
  );
};
