{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.14.4", "@tailwindcss/vite": "^4.1.11", "lucide-react": "^0.534.0", "motion": "^12.23.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-router": "^7.7.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "packageManager": "bun@1.2.0"}