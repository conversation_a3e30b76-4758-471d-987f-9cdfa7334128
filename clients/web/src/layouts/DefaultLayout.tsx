import { BlueSkyIcon } from "@/components/SvgIcons/Bluesky";
import { LinkedInIcon } from "@/components/SvgIcons/LinkedIn";
import { XIcon } from "@/components/SvgIcons/X";
import { Outlet } from "react-router";

export const Layout = () => {
  const socialLink = [
    {
      name: "X (Twitter)",
      icon: XIcon,
      id: "x-icon",
      link: "",
    },
    {
      name: "LinkedIn",
      icon: LinkedInIcon,
      id: "linkedin",
      link: "",
    },
    {
      name: "<PERSON><PERSON>",
      icon: BlueSkyIcon,
      id: "bluesky",
      link: "",
    },
  ];
  const footerLink = [
    {
      name: "Product",
      id: "product",
      links: [
        {
          link: "/features",
          name: "Feature",
          id: "feature",
        },
        {
          link: "/pricing",
          name: "Pricing",
          id: "pricing",
        },
        {
          link: "/integration",
          name: "Integration",
          id: "integration",
        },
      ],
    },
    {
      name: "Resources",
      id: "product",
      links: [
        {
          link: "/features",
          name: "Get started",
          id: "start",
        },
        {
          link: "/blog",
          name: "Blog",
          id: "blog",
        },
        {
          link: "/support",
          name: "Support",
          id: "support",
        },
      ],
    },
    {
      name: "Company",
      id: "company",
      links: [
        {
          link: "/about",
          name: "About",
          id: "about",
        },
        {
          link: "/careers",
          name: "Career",
          id: "career",
        },
        {
          link: "/contact",
          name: "Contact",
          id: "contact",
        },
      ],
    },
  ];
  const menu = [
    {
      name: "Home",
      id: "home",
      route: "/",
      sections: [
        {
          name: "",
          section: "",
        },
      ],
    },
    {
      name: "Us",
      id: "about-us",
      route: "/us",
      sections: [
        {
          name: "",
          section: "",
        },
      ],
    },
    {
      name: "feature",
      id: "features",
      route: "/features",
      sections: [
        {
          name: "",
          section: "",
        },
      ],
    },
    {
      name: "Get Started",
      id: "button",
      route: "/",
    },
  ];
  return (
    <div className="w-auto h-auto bg-[#caf0f8]">
      <div className="pt-4 flex min-h-screen flex-col items-center justify-start">
        <nav className="w-[98vw] h-12 z-1 border-[#0047a4]/25 border rounded-lg fixed bg-transparent backdrop-blur-md shadow-md flex justify-between items-center p-2">
          {/* logo */}
          <div className="h-full w-16 flex text-center items-center">
            <span className="font-extrabold text-2xl ml-3 text-[#20212f]">
              Fadhili
            </span>
          </div>

          {/* menu / menu button onsmaller screen*/}

          <div>
            <ul className="hidden flex-row gap-6 items-center md:flex">
              {menu.map((item, index) => {
                return item.id !== "button" ? (
                  <li key={index + item.id}>
                    <div className="hover:bg-[#0047a4]/65 hover:text-[#caf0f8] p-1 rounded-lg cursor-pointer">
                      {item.name}
                    </div>
                  </li>
                ) : (
                  <li>
                    {/* navigates to app login */}
                    <button
                      type="button"
                      className="border cursor-pointer hover:bg-[#caf0f8] hover:text-[#0047a4]/85 border-[#0047a4]/35 bg-[#0047a4]/65 text-[#caf0f8] p-0.75 rounded-lg"
                    >
                      {item.name}
                    </button>
                  </li>
                );
              })}
            </ul>
            <div>{/* menu drop down for mobile */}</div>
          </div>
        </nav>
        <Outlet />
      </div>
      <footer className="bg-[#0047a4]/25 h-[40vh] w-full flex justify-center items-center ">
        <div className="md:h-[34vh] w-[92vw] border-[#caf0f8]/75 border rounded-4xl bg-transparent backdrop-blur-md shadow-md flex flex-col justify-between  p-8">
          {/* details */}
          <div className="flex flex-col md:flex-row md:gap-0 gap-2 h-5/6 w-full p-2 justify-between">
            {/* desc + socials-logins */}
            <div>
              <h4 className="font-extrabold text-4xl md:text-start text-center ">Fadhili</h4>
              <p className="font-normal text-md mt-4 md:text-start text-center ">
                Empowers groups and brings trust to the table.
              </p>
              <div className="flex gap-4 mt-4 justify-center items-center md:justify-start mb-2 md:mb-0">
                {socialLink.map((social, i) => {
                  return (
                    <div
                      key={social.id + i}
                      className="underline text-xs flex flex-col justify-center items-center"
                    >
                      <div>
                        <social.icon width="28" height="28" />
                      </div>

                      <p>{social.name}</p>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* links */}
            <div className="flex flex-row gap-8">
              {/* product , resources and company */}
              {footerLink.map((item, id) => {
                return (
                  <div key={item.id + id} className="flex flex-col gap-2">
                    <span className="font-bold text-lg">{item.name}</span>
                    <ul className="flex flex-col">
                      {item.links.map((it, idd) => {
                        return (
                          <li key={idd + it.id} className="p-1">
                            {
                              <a
                                href={it.link}
                                className="text-sm cursor-pointer"
                              >
                                {it.name}
                              </a>
                            }
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>

          {/* separator */}
          <div className="border-t border-[#caf0f8]/75 "></div>

          {/* copyright */}
          <div className="flex md:flex-row flex-col gap-2 justify-between items-center p-2">
            <span className="text-xs">&copy; 2025 Fadhili. All rights reserved.</span>
            <div className="flex gap-2 text-xs">
              <p>Privacy policy</p>
              <p>Term of service</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};
