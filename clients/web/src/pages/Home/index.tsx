import { DotLottieReact } from "@lottiefiles/dotlottie-react";
export const Home = () => {
const cardContent = [
  {
    title: "Empowers Groups and Communities",
    content: "Fadhili gives social groups like chamas and SACCOs tools to govern themselves transparently and securely on the blockchain — no middlemen, just trust.",
    id: "empower-grp"
  },
  {
    title: "Real-Life Governance, Digitally",
    content: "We mirror the way real communities manage money and decisions, making it easy to create proposals, vote, and reach consensus that truly reflects your group’s values.",
    id: "governance"
  },
  {
    title: "Build Trust with Transparency",
    content: "Every transaction, vote, and decision is recorded on an immutable ledger, helping groups avoid fraud and build confidence among members.",
    id: "trust"
  },
  {
    title: "Unlock New Financial Opportunities",
    content: "Groups can now easily raise, trade, and legitimize shares, opening doors for growth, investment, and partnerships never before possible.",
    id: "finance"
  },
  {
    title: "Simple, Yet Powerful Technology — Truly Kenyan",
    content: "Fadhili is designed to be user-friendly, so anyone can join and participate in decentralized governance without needing deep technical knowledge.",
    id: "truly"
  }
]


  return (
    <div className="w-full bg-[#caf0f8] h-full pt-14 overflow-auto">
      {/* Section: Intro */}
      <div className="flex flex-col md:flex-row">
        {/* Left Column: Text */}
        <div className="md:w-[40%] w-full md:h-[calc(100vh-56px)] h-[40vh] p-6 flex flex-col items-start justify-center gap-8">
          <h1 className="text-4xl font-extrabold leading-tight text-gray-800">
            Rooted in Us: <br />A system shaped by our ideas, purpose, and
            trust.
          </h1>
          <p className="text-lg text-gray-700">
            We believe every community thrives through a cycle of shared
            purpose.
            <br />
            It begins with a single idea — a spark of intention — that grows
            into a collective will to work together toward a common goal.
            Through unity,purpose evolves into something greater:
          </p>
        </div>

        {/* Right Column: Animation */}
        <div className="md:w-[60%] w-full h-[60vh] md:h-[calc(100vh-56px)] flex items-center justify-center">
          <div className="w-5/6 h-5/6 z-[0] border-[#0047a4]/25 border rounded-lg bg-transparent backdrop-blur-md shadow-md flex justify-between items-center p-2">
            <DotLottieReact
              src="https://lottie.host/8a4d2c22-e8f8-4c23-9b21-925bab4730bb/fQmn1KLMmP.lottie"
              loop
              autoplay
              speed={3}
              
            />
          </div>
        </div>
      </div>

      {/* Section: More */}
      <section className="py-20 px-6 w-full">
  <div className="max-w-5xl mx-auto text-center flex flex-col">
    <h2 className="text-3xl font-bold mb-8 text-gray-800">Why Fadhili?</h2>
    
    <div
      className="
        flex gap-6 scroll-smooth
        overflow-x-auto snap-x snap-mandatory
        -mx-6 px-6
        sm:flex-nowrap sm:overflow-x-auto
        sm:-mx-4 sm:px-4
        md:w-full
        lg:flex-wrap lg:overflow-visible lg:justify-between lg:gap-8
      "
    >
      {cardContent.map((item, id) => (
        <div
          key={id + item.id}
          className="
            snap-start flex-shrink-0
            w-[90%]
            sm:w-[48%]
            lg:flex-grow lg:basis-[calc(20%-1.5rem)] p-6 rounded-lg shadow-md
             border-[#0047a4]/25 border bg-transparent backdrop-blur-md  flex flex-col justify-between items-center 
          "
        >
          <h4 className="text-xl font-semibold mb-3 text-gray-900">{item.title}</h4>
          <p className="text-gray-700">{item.content}</p>
        </div>
      ))}
    </div>
  </div>
</section>



    </div>
  );
};
