import type { FC } from "react";
import type { Props } from "./X";

export const BlueSkyIcon: FC<Props> = ({
    width ="100",
    height="100"
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width={width}
      height={height}
      viewBox="0,0,256,256"
    >
      <g
        fill="none"
        fill-rule="nonzero"
        stroke="none"
        stroke-width="1"
        stroke-linecap="butt"
        stroke-linejoin="miter"
        stroke-miterlimit="10"
        stroke-dasharray=""
        stroke-dashoffset="0"
        font-family="none"
        font-weight="none"
        font-size="none"
        text-anchor="none"
        style={{mixBlendMode: "normal"}}
      >
        <g transform="scale(5.33333,5.33333)">
          <path
            d="M43.722,6.417c-2.388,-1.191 -5.92,0.392 -8.426,2.274c-4.313,3.239 -8.907,9.38 -11.296,13.556c-2.389,-4.175 -6.982,-10.316 -11.295,-13.556l-0.601,0.799v0l0.6,-0.799c-2.506,-1.883 -6.037,-3.466 -8.426,-2.274c-0.82,0.41 -2.278,2.545 -2.278,2.545c0,0 0,1.193 0,2.325c0,1.228 0.718,10.853 1.204,12.591c1.067,3.817 3.865,5.755 6.998,6.49c-1.725,0.978 -3.611,3.361 -3.611,3.361c0,0 -0.014,1.501 0,2.087c0.042,1.77 0.962,3.642 2.692,5.419c2.439,2.504 4.774,3.765 6.959,3.765c0.416,0 0.826,-0.045 1.23,-0.137c3.433,-0.775 5.468,-4.688 6.527,-7.342c1.06,2.654 3.095,6.567 6.528,7.342c2.534,0.574 5.287,-0.649 8.188,-3.628c1.783,-1.831 2.706,-3.764 2.694,-5.582c-0.004,-0.53 0,-2.006 0,-2.006c0,0 -1.888,-2.302 -3.614,-3.28c3.133,-0.734 5.932,-2.672 6.999,-6.49c0.488,-1.736 1.206,-11.361 1.206,-12.59c0,-0.982 0,-2.549 0,-2.549c0,0 -1.366,-1.865 -2.278,-2.321z"
            fill="#caf0f8"
          ></path>
          <path
            d="M12.104,7.491c4.815,3.617 9.994,10.95 11.896,14.886c1.902,-3.935 7.081,-11.269 11.896,-14.886c3.475,-2.61 9.104,-4.63 9.104,1.796c0,1.283 -0.735,10.78 -1.167,12.322c-1.499,5.36 -6.962,6.727 -11.821,5.9c8.494,1.446 10.655,6.238 5.988,11.029c-8.863,9.099 -12.738,-2.283 -13.732,-5.2c-0.182,-0.535 -0.267,-0.785 -0.268,-0.572c-0.001,-0.213 -0.086,0.037 -0.268,0.572c-0.993,2.917 -4.869,14.299 -13.732,5.2c-4.667,-4.791 -2.506,-9.583 5.988,-11.029c-4.86,0.827 -10.323,-0.54 -11.821,-5.9c-0.432,-1.542 -1.167,-11.039 -1.167,-12.322c0,-6.426 5.63,-4.406 9.104,-1.796z"
            fill="#ffffff"
          ></path>
          <path
            d="M16.242,43c-2.186,0 -4.521,-1.261 -6.959,-3.765c-2.304,-2.365 -3.171,-4.9 -2.442,-7.138c0.485,-1.49 1.637,-2.752 3.361,-3.729c-3.133,-0.734 -5.931,-2.672 -6.998,-6.49c-0.486,-1.738 -1.204,-11.363 -1.204,-12.591c0,-2.476 0.767,-4.115 2.278,-4.87c2.389,-1.191 5.92,0.391 8.426,2.274h0.001c4.312,3.239 8.906,9.38 11.295,13.556c2.389,-4.176 6.983,-10.317 11.296,-13.556c2.506,-1.882 6.038,-3.465 8.426,-2.274c1.511,0.755 2.278,2.394 2.278,4.87c0,1.229 -0.718,10.854 -1.204,12.591c-1.067,3.817 -3.866,5.756 -6.999,6.49c1.726,0.978 2.877,2.24 3.362,3.729c0.729,2.237 -0.139,4.772 -2.442,7.138c-2.901,2.979 -5.654,4.202 -8.188,3.628c-3.434,-0.775 -5.469,-4.688 -6.528,-7.342c-1.06,2.655 -3.095,6.567 -6.527,7.342c-0.406,0.092 -0.816,0.137 -1.232,0.137zM24.06,31.686c0.704,0 0.934,0.675 1.156,1.33c0.602,1.768 2.434,7.147 5.753,7.896c1.806,0.406 3.932,-0.626 6.314,-3.073c1.246,-1.278 2.599,-3.205 1.975,-5.123c-0.649,-1.992 -3.352,-3.531 -7.414,-4.223c-0.544,-0.093 -0.91,-0.609 -0.817,-1.154c0.093,-0.545 0.61,-0.911 1.153,-0.818v0c0.001,0 0.002,0 0.004,0c4.266,0.725 9.308,-0.256 10.687,-5.184c0.397,-1.418 1.129,-10.821 1.129,-12.05c0,-1.656 -0.395,-2.692 -1.172,-3.081c-1.261,-0.629 -3.862,0.229 -6.331,2.083c-4.69,3.523 -9.749,10.697 -11.597,14.521c-0.167,0.345 -0.517,0.565 -0.9,0.565v0c-0.384,0 -0.733,-0.22 -0.9,-0.565c-1.848,-3.823 -6.905,-10.997 -11.596,-14.521v0c-2.469,-1.854 -5.07,-2.713 -6.332,-2.084c-0.777,0.39 -1.172,1.426 -1.172,3.082c0,1.229 0.732,10.632 1.13,12.052c1.379,4.927 6.421,5.911 10.687,5.184c0.002,0 0.003,0 0.004,0c0.546,-0.092 1.061,0.273 1.153,0.818c0.092,0.545 -0.273,1.061 -0.817,1.154v0c-4.062,0.692 -6.765,2.231 -7.414,4.222c-0.625,1.918 0.729,3.845 1.975,5.123c2.382,2.446 4.501,3.479 6.315,3.073c3.318,-0.75 5.15,-6.128 5.752,-7.896c0.229,-0.673 0.473,-1.365 1.216,-1.329c0.02,-0.001 0.039,-0.002 0.059,-0.002z"
            fill="#caf0f8"
          ></path>
        </g>
      </g>
    </svg>
  );
};
